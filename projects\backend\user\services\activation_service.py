"""
Activation Service

This service handles all activation-related functionality including:
- Activation token generation
- Activation URL construction
- Email sending
"""

from typing import Dict, Any, <PERSON><PERSON>, Optional
from django.utils.encoding import force_bytes
from django.utils.http import urlsafe_base64_encode
from oauth2_auth.secure_token_service import secure_token_service
from oauth2_auth.utils import get_client_ip
from agritram.message_utils import get_frontend_url_by_role
from user.models import User
import logging

logger = logging.getLogger(__name__)


class ActivationService:
    """Service for handling user activation token generation and email sending"""

    @staticmethod
    def generate_activation_token(
        user: User, request, device_registered: bool
    ) -> Tuple[str, Any]:
        """
        Generate secure activation token for user
        Uses thread-local storage to automatically get the unique ID.

        Args:
            user: User instance
            request: HTTP request object
            device_registered: Whether device was registered successfully

        Returns:
            Tuple of (activation_token: str, token_obj: Any)
        """
        client_ip = get_client_ip(request)
        user_agent = request.META.get("HTTP_USER_AGENT", "")

        activation_token, token_obj = secure_token_service.generate_token(
            user=user,
            token_type="activation",
            request=request,
            metadata={
                "registration_ip": client_ip,
                "registration_user_agent": user_agent,
                "device_registered": device_registered,
            },
        )

        logger.info(
            f"OPERATION_INFO: ACTIVATION_TOKEN_GENERATION | message=Generated activation token for user {user.email} | metadata="
            f"{{'user_id': {user.id}, 'user_email': '{user.email}', 'token_type': 'activation', 'device_registered': {device_registered}, 'client_ip': '{client_ip}'}}"
        )

        return activation_token, token_obj

    @staticmethod
    def construct_activation_url(user: User, activation_token: str) -> str:
        """
        Construct activation URL using role-specific frontend URL
        Uses thread-local storage to automatically get the unique ID.

        Args:
            user: User instance
            activation_token: Generated activation token

        Returns:
            str: Complete activation URL
        """
        uid = urlsafe_base64_encode(force_bytes(user.pk))
        frontend_url = get_frontend_url_by_role(user.role)
        activation_url = f"{frontend_url}/activate-account/{uid}/{activation_token}/"

        logger.info(
            f"OPERATION_INFO: ACTIVATION_URL_CONSTRUCTION | message=Constructed activation URL for user {user.email} | metadata="
            f"{{'user_id': {user.id}, 'user_email': '{user.email}', 'user_role': '{user.role}', 'frontend_url': '{frontend_url}', 'uid': '{uid}'}}"
        )

        return activation_url

    @staticmethod
    def send_activation_email(
        user: User,
        activation_url: str,
        user_agent: str,
        request_metadata: Dict[str, Any] = None,
    ) -> Tuple[bool, Optional[str]]:
        """
        Queue activation email for asynchronous delivery

        Args:
            user: User instance
            activation_url: Activation URL
            user_agent: User agent string
            request_metadata: Additional metadata from the request

        Returns:
            Tuple of (success: bool, error_message: Optional[str])
        """
        try:
            # Import the task here to avoid circular imports
            from user.tasks import send_activation_email_task

            # Queue the email task
            task_result = send_activation_email_task.delay(
                user_id=user.id,
                activation_url=activation_url,
                user_agent=user_agent,
                request_metadata=request_metadata or {},
            )

            logger.info(
                "Activation email queued successfully",
                extra={
                    "user_id": user.id,
                    "user_email": user.email,
                    "activation_url": activation_url,
                    "user_agent": user_agent,
                    "task_id": task_result.id,
                    "event_type": "ACTIVATION_EMAIL_QUEUED",
                    "operation_type": "ASYNC_EMAIL_QUEUE",
                    "request_metadata": request_metadata or {},
                },
            )

            return True, None

        except Exception as e:
            error_message = str(e)
            logger.error(
                "Failed to queue activation email",
                extra={
                    "user_id": user.id,
                    "user_email": user.email,
                    "error": error_message,
                    "error_type": e.__class__.__name__,
                    "event_type": "ACTIVATION_EMAIL_QUEUE_FAILED",
                    "operation_type": "ASYNC_EMAIL_QUEUE_ERROR",
                },
                exc_info=True,
            )

            return False, error_message

    @classmethod
    def handle_activation_flow(
        cls, user: User, request, device_registered: bool
    ) -> Dict[str, Any]:
        """
        Handle the complete activation flow
        Uses thread-local storage to automatically get the unique ID.

        Args:
            user: User instance
            request: HTTP request object
            device_registered: Whether device was registered successfully

        Returns:
            Dict containing activation flow results
        """
        user_agent = request.META.get("HTTP_USER_AGENT", "")

        # Generate activation token
        activation_token, token_obj = cls.generate_activation_token(
            user, request, device_registered
        )

        # Construct activation URL
        activation_url = cls.construct_activation_url(user, activation_token)

        # Send activation email
        email_sent, email_error = cls.send_activation_email(
            user, activation_url, user_agent
        )

        return {
            "activation_token": activation_token,
            "token_obj": token_obj,
            "activation_url": activation_url,
            "email_sent": email_sent,
            "email_error": email_error,
        }
