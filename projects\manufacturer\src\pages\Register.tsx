import AuthLayout from '@/components/AuthLayout'
import FormInput from '@/components/FormInput'
import { PasswordStrength } from '@/components/PasswordStrength'
import { useToast } from '@/components/ui/use-toast'
import { registerUser } from '@/services/authService'
import { useState } from 'react'
import { Link, useNavigate } from 'react-router-dom'

const Register = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    confirmPassword: '',
  })
  const [errors, setErrors] = useState<{ [key: string]: string }>({})
  const [isLoading, setIsLoading] = useState(false)
  const { toast } = useToast()
  const navigate = useNavigate()

  const validateForm = () => {
    const newErrors: { [key: string]: string } = {}

    if (!formData.name) {
      newErrors.name = 'Name is required'
    }

    if (!formData.email) {
      newErrors.email = 'Email is required'
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Email is invalid'
    }

    if (!formData.password) {
      newErrors.password = 'Password is required'
    } else if (formData.password.length < 12) {
      newErrors.password = 'Password must be at least 12 characters for security'
    } else {
      // Check for fintech-grade requirements
      const hasUpper = /[A-Z]/.test(formData.password)
      const hasLower = /[a-z]/.test(formData.password)
      const hasDigit = /\d/.test(formData.password)
      const hasSpecial = /[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?`~]/.test(formData.password)

      if (!hasUpper || !hasLower || !hasDigit || !hasSpecial) {
        newErrors.password = 'Password must contain uppercase, lowercase, digit, and special character'
      }
    }

    if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) return

    setIsLoading(true)

    try {
      await registerUser({
        name: formData.name,
        email: formData.email,
        password: formData.password,
      })

      toast({
        title: 'Success!',
        description: 'Your account has been created successfully. Please check your email for verification.',
      })
      // Redirect user to login page, or any other route as needed
      navigate('/login')
    } catch (error) {
      console.error('Registration error:', error)
      toast({
        title: 'Error!',
        description: 'An error occurred during registration. Please try again with different credentials.',
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleChange = (field: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }))
  }

  return (
    <AuthLayout title="Create Account" subtitle="Sign up to get started with our platform">
      <form onSubmit={handleSubmit} className="space-y-6">
        <FormInput
          label="Full Name"
          type="text"
          value={formData.name}
          onChange={(value) => handleChange('name', value)}
          error={errors.name}
          placeholder="Enter your full name"
        />
        <FormInput
          label="Email"
          type="email"
          value={formData.email}
          onChange={(value) => handleChange('email', value)}
          error={errors.email}
          placeholder="Enter your email"
        />
        <FormInput
          label="Password"
          type="password"
          value={formData.password}
          onChange={(value) => handleChange('password', value)}
          error={errors.password}
          placeholder="Create a password"
        />
        <PasswordStrength password={formData.password} />
        <FormInput
          label="Confirm Password"
          type="password"
          value={formData.confirmPassword}
          onChange={(value) => handleChange('confirmPassword', value)}
          error={errors.confirmPassword}
          placeholder="Confirm your password"
        />

        <button
          type="submit"
          disabled={isLoading}
          className={`w-full py-3 px-4 bg-button-bg text-button-text rounded-lg font-medium ${
            isLoading ? 'opacity-50 cursor-not-allowed' : 'hover:bg-button-bg-hover'
          }`}
        >
          {isLoading ? 'Creating account...' : 'Create Account'}
        </button>

        <p className="text-center text-primary-text">
          <span className="opacity-60">Already have an account? </span>
          <Link to="/login" className="text-link-text hover:text-link-text/80 font-bold">
            Sign in
          </Link>
        </p>
      </form>
    </AuthLayout>
  )
}

export default Register
