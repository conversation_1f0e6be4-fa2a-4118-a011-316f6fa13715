from django.urls import path

from .views import (
    activate_account,
    check_password_strength,
    connect_account,
    forgot_password,
    get_user_name,
    login,
    logout,
    opt_in_account,
    refresh_token,
    register,
    resend_device_otp,
    reset_password,
    reset_password_confirm,
    update_user,
)

urlpatterns = [
    path("register/", register),
    path("login/", login),
    path("logout/", logout),
    path("refresh-token/", refresh_token),
    path("profile/", update_user),  # GET for profile, PUT for updates
    path("update/", update_user),  # Backward compatibility
    path("reset-password/", reset_password),
    path("forgot-password/", forgot_password),
    path("reset-password-confirm/", reset_password_confirm),
    path("get-user-name/", get_user_name),
    path("activate-account/", activate_account),
    path("activate-account/<str:uid>/<str:token>/", activate_account),
    path("connect-account/", connect_account),
    path("opt-in-account/", opt_in_account),
    path("resend-device-otp/", resend_device_otp),
    path("check-password-strength/", check_password_strength),
]
