"""
Registration Orchestrator Service

This service coordinates all registration-related services to handle the complete
user registration flow in a structured and maintainable way.
"""

import copy
import logging
from typing import Dict, Any
import json
import uuid

from oauth2_auth.utils import get_client_ip, generate_device_fingerprint
from agritram.message_utils import StandardSuccessResponse
from agritram.exceptions import DuplicateResourceException
from .registration_validation_service import RegistrationValidationService
from .device_management_service import DeviceManagementService
from .user_creation_service import UserCreationService
from .activation_service import ActivationService

logger = logging.getLogger(__name__)


class RegistrationOrchestrator:
    """Main orchestrator for the user registration process"""

    def __init__(self):
        self.validation_service = RegistrationValidationService()
        self.device_service = DeviceManagementService()
        self.user_service = UserCreationService()
        self.activation_service = ActivationService()

    @staticmethod
    def _prepare_logging_data(
        request_data: Dict[str, Any], headers: Dict[str, str]
    ) -> str:
        """Prepare request data and headers for logging with sensitive data masked"""
        # Create a copy to avoid modifying original data
        safe_data = copy.deepcopy(request_data)

        # Remove password completely
        if "password" in safe_data:
            del safe_data["password"]

        # Prepare headers (exclude sensitive ones)
        safe_headers = {}
        header_whitelist = [
            "HTTP_USER_AGENT",
            "HTTP_ACCEPT",
            "HTTP_ACCEPT_LANGUAGE",
            "HTTP_ACCEPT_ENCODING",
            "CONTENT_TYPE",
            "CONTENT_LENGTH",
            "HTTP_ORIGIN",
            "HTTP_REFERER",
            "HTTP_X_FORWARDED_FOR",
            "REMOTE_ADDR",
            "HTTP_HOST",
        ]

        for key in header_whitelist:
            if key in headers:
                safe_headers[key] = headers[key]

        # Combine data and headers
        log_data = {"request_data": safe_data, "headers": safe_headers}

        # Return as single-line JSON
        return json.dumps(log_data, separators=(",", ":"))

    def register_user(
        self, request, debug_mode: bool = False, extended_mode: bool = False
    ) -> Dict[str, Any]:
        """
        Orchestrate the complete user registration process

        Args:
            request: HTTP request object
            debug_mode: Include detailed response data for debugging
            extended_mode: Include extended response data

        Returns:
            Dict containing registration response data

        Raises:
            Various exceptions based on validation failures
        """
        # Generate unique request ID for this registration attempt
        request_id = str(uuid.uuid4())

        # Initialize variables for exception handling
        device_data = {}

        try:
            # Log incoming request with debug details
            logger.info(
                "Registration request received",
                extra={
                    "operation": "REGISTRATION_REQUEST",
                    "request_id": request_id,
                    "method": request.method,
                    "path": request.path,
                },
            )

            # Log request data and headers with sensitive data masked (single-line JSON)
            try:
                # Safely get request data without causing body access issues
                request_data = {}
                if hasattr(request, "data") and request.data:
                    request_data = dict(request.data)
                elif hasattr(request, "POST") and request.POST:
                    request_data = dict(request.POST)
                headers = dict(request.META)
                masked_data_json = self._prepare_logging_data(request_data, headers)

                logger.info(
                    f"Registration request data: {masked_data_json}",
                    extra={
                        "operation": "REGISTRATION_REQUEST_DATA",
                        "request_id": request_id,
                    },
                )
            except Exception as e:
                logger.warning(
                    f"Failed to log request data: {str(e)}",
                    extra={
                        "operation": "REGISTRATION_REQUEST_DATA_ERROR",
                        "request_id": request_id,
                    },
                )

            logger.debug(
                "Registration request details",
                extra={
                    "operation": "REGISTRATION_REQUEST_DEBUG",
                    "request_id": request_id,
                    "content_type": request.content_type,
                    "method": request.method,
                },
            )

            # Get client information for security logging
            client_ip = get_client_ip(request)
            user_agent = request.META.get("HTTP_USER_AGENT", "")
            fingerprint = generate_device_fingerprint(request)

            logger.debug(
                "Client information extracted",
                extra={
                    "operation": "CLIENT_INFO_DEBUG",
                    "request_id": request_id,
                    "client_ip": client_ip,
                    "user_agent": (
                        user_agent[:100] + "..."
                        if len(user_agent) > 100
                        else user_agent
                    ),
                    "fingerprint": fingerprint,
                },
            )

            # Step 1: Validate registration request
            logger.info(
                "Starting registration validation",
                extra={
                    "operation": "REGISTRATION_VALIDATION_START",
                    "request_id": request_id,
                    "client_ip": client_ip,
                    "user_agent": (
                        user_agent[:50] + "..." if len(user_agent) > 50 else user_agent
                    ),
                },
            )

            device_data = self.validation_service.validate_registration_request(request)

            logger.info(
                "Registration validation completed successfully",
                extra={
                    "operation": "REGISTRATION_VALIDATION_COMPLETE",
                    "request_id": request_id,
                    "email": device_data["email"],
                    "device_id": device_data["device_id"],
                },
            )

            logger.debug(
                "Validation result details",
                extra={
                    "operation": "VALIDATION_RESULT_DEBUG",
                    "request_id": request_id,
                    "device_name": device_data.get("device_name"),
                    "device_type": device_data.get("device_type"),
                    "user_provided_device_id": device_data.get(
                        "user_provided_device_id"
                    ),
                },
            )

            # Step 2: Create user
            logger.info(
                "Starting user creation",
                extra={
                    "operation": "USER_CREATION_START",
                    "request_id": request_id,
                    "email": device_data["email"],
                },
            )

            user_creation_result = self.user_service.handle_user_creation_flow(
                request, device_data, fingerprint
            )

            if not user_creation_result["success"]:
                # Log failed registration attempt
                logger.warning(
                    f"SECURITY_EVENT: FAILED_REGISTRATION | description=Registration failed due to validation errors | metadata="
                    f"{{'email': '{device_data['email']}', 'errors': {json.dumps(user_creation_result['serializer'].errors)}, 'device_name': '{device_data['device_name']}'}}"
                )

                return user_creation_result["error_response"]

            user = user_creation_result["user"]

            logger.info(
                f"User creation completed successfully for {user.email}",
                extra={
                    "operation": "USER_CREATION_COMPLETE",
                    "user_id": user.id,
                },
            )

            # Step 3: Handle device registration
            logger.info(
                "Starting device registration",
                extra={
                    "operation": "DEVICE_REGISTRATION_START",
                    "user_id": user.id,
                    "device_id": device_data["device_id"],
                },
            )

            device_result = self.device_service.handle_device_registration_flow(
                user, device_data, request
            )

            device_registered = device_result["device_registered"]

            logger.info(
                f"Device registration completed for {user.email}",
                extra={
                    "operation": "DEVICE_REGISTRATION_COMPLETE",
                    "user_id": user.id,
                    "device_registered": device_registered,
                    "device_id": device_data["device_id"],
                },
            )

            # Step 4: Handle activation flow
            logger.info(
                "Starting activation flow",
                extra={"operation": "ACTIVATION_FLOW_START", "user_id": user.id},
            )

            activation_result = self.activation_service.handle_activation_flow(
                user, request, device_registered
            )

            logger.info(
                f"Activation flow completed for {user.email}",
                extra={
                    "operation": "ACTIVATION_FLOW_COMPLETE",
                    "user_id": user.id,
                    "email_sent": activation_result["email_sent"],
                },
            )

            # Step 5: Log comprehensive registration event
            self.user_service.log_user_registration_event(
                user,
                device_data,
                False,  # oauth2_app_created = False since we removed OAuth2 creation
                device_registered,
                fingerprint,
            )

            # Step 6: Prepare response data (always include device info)
            response_data = {"device": {"id": device_result["device_id"]}}

            # Add additional data if debug or extended mode is enabled
            if debug_mode or extended_mode:
                additional_data = self._prepare_response_data(
                    user_creation_result,
                    device_result,
                    None,  # oauth2_application = None since we removed OAuth2 creation
                    debug_mode,
                    extended_mode,
                )
                response_data.update(additional_data)

            # Log successful registration response with data
            try:
                response_data_json = json.dumps(response_data, separators=(",", ":"))
                logger.info(
                    f"Registration successful - Response data: {response_data_json}",
                    extra={
                        "operation": "REGISTRATION_SUCCESS",
                        "status_code": 201,
                        "user_id": user.id,
                        "user_email": user.email,
                        "device_registered": device_registered,
                        "debug_mode": debug_mode,
                        "extended_mode": extended_mode,
                    },
                )
            except Exception as e:
                # Fallback to basic logging if JSON serialization fails
                logger.info(
                    f"Registration successful - Response data serialization failed: {str(e)}",
                    extra={
                        "operation": "REGISTRATION_SUCCESS",
                        "status_code": 201,
                        "user_id": user.id,
                        "user_email": user.email,
                        "device_registered": device_registered,
                        "debug_mode": debug_mode,
                        "extended_mode": extended_mode,
                    },
                )

            return StandardSuccessResponse.registration_success(
                message="Registration successful",
                details="Account created and activation email sent to your email address",
                user_data=response_data,
            )

        except DuplicateResourceException:
            # Log duplicate registration attempt
            email = device_data.get("email", "unknown") if device_data else "unknown"
            logger.warning(
                "Registration attempt with existing email",
                extra={"operation": "DUPLICATE_REGISTRATION", "email": email},
            )
            # Re-raise our custom exceptions to be handled by the exception handler
            raise

        except Exception as e:
            # Check if it's one of our custom API exceptions
            if hasattr(e, "code") and hasattr(e, "message"):
                # This is likely an AgritramAPIException, re-raise to be handled by exception handler
                raise
            # Log unexpected registration error
            email = device_data.get("email", "unknown") if device_data else "unknown"
            logger.error(
                f"Unexpected registration error: {str(e)}",
                extra={
                    "operation": "REGISTRATION_ERROR",
                    "error": str(e),
                    "error_type": e.__class__.__name__,
                    "email": email,
                },
                exc_info=True,
            )
            raise

    def _prepare_response_data(
        self,
        user_creation_result: Dict[str, Any],
        device_result: Dict[str, Any],
        oauth2_application,  # Will be None since OAuth2 creation is removed
        debug_mode: bool = False,
        extended_mode: bool = False,
    ) -> Dict[str, Any]:
        """
        Prepare response data for successful registration

        Args:
            user_creation_result: Result from user creation flow
            device_result: Result from device registration flow
            oauth2_application: OAuth2 application instance (unused, will be None)
            debug_mode: Include debug information
            extended_mode: Include extended information

        Returns:
            Dict containing response data based on requested verbosity
        """
        # Base data for extended mode
        if extended_mode:
            user_data = {"device": {"id": device_result["device_id"]}}
        else:
            user_data = {}

        # Additional debug information
        if debug_mode:
            user_data.update(
                {
                    "user": user_creation_result["serializer"].data,
                    "activation_required": True,
                    "device_registered": device_result["device_registered"],
                    "device_id": device_result["device_id"],
                    "device_name": device_result["device_name"],
                }
            )

            # OAuth2 client info removed - will be handled in separate function

        return user_data
