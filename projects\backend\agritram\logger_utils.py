import uuid
import logging
import inspect
import threading

logger = logging.getLogger(__name__)

# Thread-local storage for request context
_thread_local = threading.local()


def get_client_ip(request):
    """
    Get client IP address from request
    """
    x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
    if x_forwarded_for:
        ip = x_forwarded_for.split(",")[0].strip()
    else:
        ip = request.META.get("REMOTE_ADDR")
    return ip


def generate_unique_id():
    """
    Generate a unique identifier for logging correlation.
    Format: <full_uuid>

    This is the single function for generating unique IDs throughout the application.
    Replaces both generate_unique_request_id() and generate_api_call_uuid().
    """
    return f"{str(uuid.uuid4())}"


def generate_correlation_id():
    """
    Generate a correlation ID for request tracking.
    Alias for generate_unique_id for compatibility with correlation ID patterns.
    """
    return generate_unique_id()


def set_request_context(unique_id=None, **context):
    """
    Set the request context for the current thread.

    Args:
        unique_id (str): Unique identifier for the request (optional)
        **context: Additional context data
    """
    if unique_id:
        _thread_local.unique_id = unique_id
    _thread_local.context = context


def get_current_unique_id():
    """
    Get the current unique ID from thread-local storage.
    If no ID is set, try to get it from request context, otherwise generate a new one.

    Returns:
        str: Current unique ID for the thread
    """
    # First check if unique_id is directly stored in thread-local
    if hasattr(_thread_local, "unique_id") and _thread_local.unique_id:
        return _thread_local.unique_id

    # If not found, try to get it from the context (middleware might have stored it there)
    context = getattr(_thread_local, "context", {})
    if "unique_id" in context:
        _thread_local.unique_id = context["unique_id"]
        return _thread_local.unique_id

    # If still not found, generate a new one
    _thread_local.unique_id = generate_unique_id()
    return _thread_local.unique_id


def get_request_context():
    """
    Get the current request context from thread-local storage.

    Returns:
        dict: Current context data
    """
    return getattr(_thread_local, "context", {})


def clear_request_context():
    """
    Clear the request context for the current thread.
    """
    if hasattr(_thread_local, "unique_id"):
        delattr(_thread_local, "unique_id")
    if hasattr(_thread_local, "context"):
        delattr(_thread_local, "context")


class LoggingContext:
    """
    Context manager for setting logging context with automatic cleanup.

    Usage:
        with LoggingContext(operation="user_registration", user_id=123):
            enhanced_log("Starting user registration")
            # All logs in this block will share the same unique ID
            some_function()  # This function's logs will also use the same ID
    """

    def __init__(self, **context):
        self.context = context
        self.previous_unique_id = None
        self.previous_context = None

    def __enter__(self):
        # Save previous context
        self.previous_context = getattr(_thread_local, "context", {})

        # Set new context
        set_request_context(**self.context)
        return get_current_unique_id()

    def __exit__(self, exc_type, exc_val, exc_tb):
        # Restore previous context
        _thread_local.context = self.previous_context


def get_caller_info():
    """
    Get information about the caller (file name, line number, function name).
    Returns a dictionary with caller details.
    """
    try:
        # Get the current frame and go up the stack to find the actual caller
        frame = inspect.currentframe()
        caller_frame = (
            frame.f_back.f_back
        )  # Go up two levels to skip this function and the logging function

        if caller_frame:
            filename = caller_frame.f_code.co_filename.split("/")[
                -1
            ]  # Get just the filename
            line_number = caller_frame.f_lineno
            function_name = caller_frame.f_code.co_name

            return {
                "filename": filename,
                "line_number": line_number,
                "function_name": function_name,
            }
    except Exception:
        pass

    return {"filename": "unknown", "line_number": 0, "function_name": "unknown"}
