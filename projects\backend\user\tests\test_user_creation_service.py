"""
Tests for User Creation Service

This module contains comprehensive tests for the UserCreationService
to ensure all user creation operations work correctly.
"""

import pytest
from unittest.mock import Mock, patch
from django.test import TestCase, RequestFactory
from user.services.user_creation_service import UserCreationService


class TestUserCreationService(TestCase):
    """Test cases for UserCreationService"""

    def setUp(self):
        """Set up test fixtures"""
        self.factory = RequestFactory()
        self.service = UserCreationService()
        self.unique_id = "test-unique-id"

    @patch("user.services.user_creation_service.detect_user_role_from_request")
    def test_prepare_registration_data(self, mock_detect_role):
        """Test registration data preparation"""
        # Arrange
        mock_detect_role.return_value = "farmer"
        request = self.factory.post(
            "/register/",
            {
                "email": "<EMAIL>",
                "name": "Test User",
                "password": "testpass123",
            },
        )

        # Act
        result = self.service.prepare_registration_data(request)

        # Assert
        self.assertEqual(result["email"], "<EMAIL>")
        self.assertEqual(result["name"], "Test User")
        self.assertEqual(result["role"], "farmer")
        mock_detect_role.assert_called_once_with(request)

    @patch("user.services.user_creation_service.UserSerializer")
    @patch("user.services.user_creation_service.log_operation_info")
    def test_create_user_success(self, mock_log, mock_serializer_class):
        """Test successful user creation"""
        # Arrange
        mock_serializer = Mock()
        mock_serializer.is_valid.return_value = True
        mock_user = Mock()
        mock_user.id = 1
        mock_user.email = "<EMAIL>"
        mock_user.role = "farmer"
        mock_user.name = "Test User"
        mock_serializer.save.return_value = mock_user
        mock_serializer_class.return_value = mock_serializer

        registration_data = {
            "email": "<EMAIL>",
            "name": "Test User",
            "password": "testpass123",
            "role": "farmer",
        }

        # Act
        user, serializer, is_valid = self.service.create_user(
            registration_data, self.unique_id
        )

        # Assert
        self.assertEqual(user, mock_user)
        self.assertEqual(serializer, mock_serializer)
        self.assertTrue(is_valid)
        mock_serializer_class.assert_called_once_with(data=registration_data)
        mock_serializer.is_valid.assert_called_once()
        mock_serializer.save.assert_called_once()

    @patch("user.services.user_creation_service.UserSerializer")
    @patch("user.services.user_creation_service.log_operation_info")
    def test_create_user_validation_failure(self, mock_log, mock_serializer_class):
        """Test user creation with validation failure"""
        # Arrange
        mock_serializer = Mock()
        mock_serializer.is_valid.return_value = False
        mock_serializer.errors = {"email": ["This field is required."]}
        mock_serializer_class.return_value = mock_serializer

        registration_data = {"name": "Test User"}  # Missing email

        # Act
        user, serializer, is_valid = self.service.create_user(
            registration_data, self.unique_id
        )

        # Assert
        self.assertIsNone(user)
        self.assertEqual(serializer, mock_serializer)
        self.assertFalse(is_valid)
        mock_log.assert_called_once()

    @patch("user.services.user_creation_service.Application.objects.create")
    @patch("user.services.user_creation_service.get_frontend_url_by_role")
    @patch("user.services.user_creation_service.secrets")
    @patch("user.services.user_creation_service.log_operation_info")
    def test_create_oauth2_application_success(
        self, mock_log, mock_secrets, mock_frontend_url, mock_create
    ):
        """Test successful OAuth2 application creation"""
        # Arrange
        mock_frontend_url.return_value = "https://farmer.example.com"
        mock_secrets.token_urlsafe.side_effect = ["client-secret", "app-secret"]
        mock_application = Mock()
        mock_application.client_id = "agritram-1-client-secret"
        mock_application.name = "Agritram App - Test User"
        mock_application.redirect_uris = "https://farmer.example.com/auth/callback/"
        mock_create.return_value = mock_application

        user = Mock()
        user.id = 1
        user.email = "<EMAIL>"
        user.name = "Test User"
        user.role = "farmer"

        # Act
        application, success = self.service.create_oauth2_application(
            user, self.unique_id
        )

        # Assert
        self.assertEqual(application, mock_application)
        self.assertTrue(success)
        mock_create.assert_called_once()
        mock_log.assert_called_once()

    @patch("user.services.user_creation_service.Application.objects.create")
    @patch("user.services.user_creation_service.get_frontend_url_by_role")
    @patch("user.services.user_creation_service.log_operation_info")
    def test_create_oauth2_application_failure(
        self, mock_log, mock_frontend_url, mock_create
    ):
        """Test OAuth2 application creation failure"""
        # Arrange
        mock_frontend_url.return_value = "https://farmer.example.com"
        mock_create.side_effect = Exception("Database error")

        user = Mock()
        user.id = 1
        user.email = "<EMAIL>"
        user.name = "Test User"
        user.role = "farmer"

        # Act
        application, success = self.service.create_oauth2_application(
            user, self.unique_id
        )

        # Assert
        self.assertIsNone(application)
        self.assertFalse(success)
        mock_log.assert_called_with(
            self.unique_id,
            "OAUTH_APP_CREATION",
            f"Failed to create OAuth2 application for user {user.email}",
            metadata={
                "user_email": user.email,
                "user_id": user.id,
                "error": "Database error",
                "error_type": "Exception",
            },
            level="ERROR",
        )

    @patch("user.services.user_creation_service.log_business_event")
    def test_log_user_registration_event(self, mock_log_business):
        """Test user registration event logging"""
        # Arrange
        user = Mock()
        user.id = 1
        user.email = "<EMAIL>"
        user.name = "Test User"
        user.role = "farmer"

        device_data = {
            "device_name": "Test Device",
            "device_id": "test-device-id",
            "device_type": "web",
        }

        # Act
        self.service.log_user_registration_event(
            user, device_data, True, True, "test-fingerprint", self.unique_id
        )

        # Assert
        mock_log_business.assert_called_once_with(
            self.unique_id,
            "USER_REGISTERED",
            "New user registration completed successfully",
            entity_type="USER",
            entity_id="1",
            metadata={
                "user_id": 1,
                "email": "<EMAIL>",
                "name": "Test User",
                "role": "farmer",
                "device_name": "Test Device",
                "device_id": "test-device-id",
                "device_type": "web",
                "fingerprint": "test-fingerprint",
                "oauth2_app_created": True,
                "device_registered": True,
            },
        )

    @patch.object(UserCreationService, "prepare_registration_data")
    @patch.object(UserCreationService, "create_user")
    @patch("user.services.user_creation_service.handle_registration_serializer_errors")
    def test_handle_user_creation_flow_success(
        self, mock_handle_errors, mock_create, mock_prepare
    ):
        """Test successful user creation flow"""
        # Arrange
        mock_prepare.return_value = {"email": "<EMAIL>", "role": "farmer"}
        mock_user = Mock()
        mock_serializer = Mock()
        mock_create.return_value = (mock_user, mock_serializer, True)

        request = Mock()
        device_data = {"device_name": "Test Device"}

        # Act
        result = self.service.handle_user_creation_flow(
            request, device_data, "test-fingerprint", self.unique_id
        )

        # Assert
        self.assertTrue(result["success"])
        self.assertEqual(result["user"], mock_user)
        self.assertEqual(result["serializer"], mock_serializer)
        # OAuth2 application creation removed from registration flow

    @patch.object(UserCreationService, "prepare_registration_data")
    @patch.object(UserCreationService, "create_user")
    @patch("user.services.user_creation_service.handle_registration_serializer_errors")
    def test_handle_user_creation_flow_validation_failure(
        self, mock_handle_errors, mock_create, mock_prepare
    ):
        """Test user creation flow with validation failure"""
        # Arrange
        mock_prepare.return_value = {"email": "<EMAIL>"}
        mock_serializer = Mock()
        mock_create.return_value = (None, mock_serializer, False)
        mock_handle_errors.return_value = {"error": "Validation failed"}

        request = Mock()
        device_data = {"device_name": "Test Device"}

        # Act
        result = self.service.handle_user_creation_flow(
            request, device_data, "test-fingerprint", self.unique_id
        )

        # Assert
        self.assertFalse(result["success"])
        self.assertIsNone(result["user"])
        self.assertEqual(result["error_response"], {"error": "Validation failed"})


if __name__ == "__main__":
    pytest.main([__file__])
