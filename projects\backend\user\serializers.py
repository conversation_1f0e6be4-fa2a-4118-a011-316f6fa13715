import logging
from django.core.exceptions import ValidationError
from rest_framework import serializers
from .models import User
from .password_validators import validate_fintech_password
from agritram.exceptions import DuplicateResourceException
from django.db import IntegrityError

logger = logging.getLogger(__name__)


class UserSerializer(serializers.ModelSerializer):
    password = serializers.CharField(write_only=True)

    class Meta:
        model = User
        fields = [
            "id",
            "name",
            "email",
            "password",
            "role",
            "account_address",
            "opt_in",
        ]
        extra_kwargs = {
            "password": {"write_only": True},
        }

    def create(self, validated_data):
        """
        Create a new User instance with a hashed password.
        Handles race conditions for duplicate email addresses.
        """
        # Validate email domain before creating user
        email = validated_data.get("email")
        if email:
            from user.email_domain_validator import email_domain_validator

            is_valid, error_message, _ = email_domain_validator.validate_email_domain(
                email
            )
            if not is_valid:
                raise serializers.ValidationError(
                    {"email": error_message or "Invalid email domain"}
                )

        password = validated_data.pop("password")
        user = User(**validated_data)

        try:
            # Use our enhanced fintech password validation
            validate_fintech_password(password, user)
        except ValidationError as e:
            raise serializers.ValidationError({"error": e.messages})

        user.set_password(password)

        try:
            user.save()
            logger.info(
                "User created successfully",
                extra={
                    "operation": "USER_CREATION_SUCCESS",
                    "user_email": user.email,
                    "user_role": getattr(user, "role", "unknown"),
                },
            )
            return user

        except IntegrityError as e:
            # Handle race condition where another request created user with same email
            logger.warning(
                "Race condition detected during user creation",
                extra={
                    "operation": "USER_CREATION_RACE_CONDITION",
                    "email": validated_data.get("email", "unknown"),
                    "error": str(e),
                },
            )

            # Check if it's specifically an email uniqueness constraint violation
            if "email" in str(e).lower() and "unique" in str(e).lower():
                raise DuplicateResourceException(
                    message="An account with this email address already exists",
                    details="Another user was created with this email address while your request was being processed. Please try logging in instead.",
                )
            else:
                # Re-raise other integrity errors
                logger.error(
                    "Unexpected integrity error during user creation",
                    extra={
                        "operation": "USER_CREATION_INTEGRITY_ERROR",
                        "email": validated_data.get("email", "unknown"),
                        "error": str(e),
                    },
                )
                raise serializers.ValidationError(
                    {
                        "error": "Unable to create account due to data integrity constraints"
                    }
                )

    def update(self, instance, validated_data):
        """
        Update an existing User instance. If a new password is provided,
        hash it before saving.
        """
        if "password" in validated_data:
            raise serializers.ValidationError(
                {"password": "Password update is not allowed in this endpoint."}
            )
        if "email" in validated_data:
            raise serializers.ValidationError(
                {"email": "Email update is not allowed. Please contact support."}
            )
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        return instance
