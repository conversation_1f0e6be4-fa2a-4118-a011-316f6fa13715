#!/usr/bin/env python
"""
Email Queue System Guide and Management Tool

This script demonstrates how the email queue system works and provides
tools for monitoring and managing email processing.
"""

import os
import sys
import django
import json
from datetime import datetime

# Setup Django
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'agritram.settings')
django.setup()


def explain_email_system():
    """Explain how the email system works"""
    
    print("📧 EMAIL QUEUE SYSTEM OVERVIEW")
    print("=" * 50)
    
    print("\n🔧 ARCHITECTURE:")
    print("   1. Django App → Queues Email Task")
    print("   2. Redis → Stores Task Queue")
    print("   3. Celery Worker → Processes Tasks")
    print("   4. Email Service → Sends Emails")
    
    print("\n📋 COMPONENTS:")
    print("   • Redis: Message broker (queue storage)")
    print("   • Celery: Task queue system")
    print("   • Email Tasks: Background email processing")
    print("   • Email Service: SMTP email sending")
    
    print("\n⚡ FLOW:")
    print("   Registration → Queue Activation Email → Worker Processes → Email Sent")


def check_system_status():
    """Check if the email system components are running"""
    
    print("\n🔍 SYSTEM STATUS CHECK")
    print("=" * 30)
    
    # Check Redis connection
    try:
        from django.core.cache import cache
        cache.set('test_key', 'test_value', 10)
        result = cache.get('test_key')
        if result == 'test_value':
            print("✅ Redis: Connected and working")
        else:
            print("❌ Redis: Connection issue")
    except Exception as e:
        print(f"❌ Redis: Error - {e}")
    
    # Check Celery workers
    try:
        from celery import current_app
        inspect = current_app.control.inspect()
        stats = inspect.stats()
        
        if stats:
            print(f"✅ Celery Workers: {len(stats)} active worker(s)")
            for worker_name in stats.keys():
                print(f"   • {worker_name}")
        else:
            print("❌ Celery Workers: No active workers found")
            print("   → Start with: python manage.py start_celery_worker")
    except Exception as e:
        print(f"❌ Celery Workers: Error - {e}")
    
    # Check email configuration
    try:
        from django.conf import settings
        email_backend = getattr(settings, 'EMAIL_BACKEND', 'Not configured')
        print(f"✅ Email Backend: {email_backend}")
        
        if hasattr(settings, 'EMAIL_HOST'):
            print(f"   • Host: {settings.EMAIL_HOST}")
            print(f"   • Port: {getattr(settings, 'EMAIL_PORT', 'Not set')}")
            print(f"   • TLS: {getattr(settings, 'EMAIL_USE_TLS', 'Not set')}")
    except Exception as e:
        print(f"❌ Email Configuration: Error - {e}")


def show_queue_statistics():
    """Show current queue statistics"""
    
    print("\n📊 QUEUE STATISTICS")
    print("=" * 25)
    
    try:
        from user.async_email_service import async_email_service
        
        stats = async_email_service.get_queue_stats()
        
        print(f"Active Tasks: {stats.get('active_tasks_count', 0)}")
        print(f"Scheduled Tasks: {stats.get('scheduled_tasks_count', 0)}")
        
        # Show active tasks details
        active_tasks = stats.get('active_tasks', {})
        if active_tasks:
            print("\n📋 Active Tasks:")
            for worker, tasks in active_tasks.items():
                print(f"   Worker: {worker}")
                for task in tasks:
                    print(f"     • {task.get('name', 'Unknown')} (ID: {task.get('id', 'N/A')[:8]}...)")
        else:
            print("   No active tasks")
        
        # Show worker stats
        worker_stats = stats.get('worker_stats', {})
        if worker_stats:
            print("\n🔧 Worker Statistics:")
            for worker, worker_info in worker_stats.items():
                pool_info = worker_info.get('pool', {})
                print(f"   Worker: {worker}")
                print(f"     • Pool: {pool_info.get('implementation', 'N/A')}")
                print(f"     • Processes: {pool_info.get('processes', 'N/A')}")
                print(f"     • Max Concurrency: {pool_info.get('max-concurrency', 'N/A')}")
        
    except Exception as e:
        print(f"❌ Error getting queue stats: {e}")


def test_email_queue():
    """Test the email queue by sending a test email"""
    
    print("\n🧪 TESTING EMAIL QUEUE")
    print("=" * 30)
    
    try:
        from django.contrib.auth import get_user_model
        from user.async_email_service import async_email_service
        
        User = get_user_model()
        
        # Find a test user or create one
        test_user = User.objects.filter(email__icontains='test').first()
        
        if not test_user:
            print("❌ No test user found")
            print("   Create a test user first or use an existing user")
            return
        
        print(f"📤 Queueing test activation email for: {test_user.email}")
        
        # Queue a test activation email
        task_result = async_email_service.send_activation_email_async(
            user=test_user,
            activation_url="http://localhost:5173/activate/test-token",
            user_agent="Email Queue Test",
            request_metadata={"test": True}
        )
        
        print(f"✅ Email queued successfully!")
        print(f"   Task ID: {task_result.id}")
        print(f"   Status: {task_result.status}")
        
        # Check task status after a moment
        import time
        time.sleep(2)
        
        task_status = async_email_service.get_task_status(task_result.id)
        print(f"\n📋 Task Status (after 2 seconds):")
        print(f"   Status: {task_status.get('status', 'Unknown')}")
        print(f"   Ready: {task_status.get('ready', False)}")
        print(f"   Successful: {task_status.get('successful', False)}")
        
        if task_status.get('ready') and task_status.get('successful'):
            print("🎉 Email task completed successfully!")
        elif task_status.get('ready') and not task_status.get('successful'):
            print("❌ Email task failed")
            result = task_status.get('result', {})
            if isinstance(result, dict) and 'error' in result:
                print(f"   Error: {result['error']}")
        else:
            print("⏳ Email task still processing...")
            print("   Check logs or wait a bit longer")
        
    except Exception as e:
        print(f"❌ Error testing email queue: {e}")


def show_management_commands():
    """Show available management commands"""
    
    print("\n🛠️  MANAGEMENT COMMANDS")
    print("=" * 30)
    
    print("📋 Starting Services:")
    print("   1. Start Redis:")
    print("      redis-server")
    print()
    print("   2. Start Celery Worker:")
    print("      python manage.py start_celery_worker")
    print("      python manage.py start_celery_worker --concurrency=4")
    print("      python manage.py start_celery_worker --queue=email")
    print()
    print("   3. Start Celery Beat (for periodic tasks):")
    print("      celery -A agritram beat --loglevel=info")
    print()
    print("   4. Monitor with Flower (optional):")
    print("      pip install flower")
    print("      celery -A agritram flower")
    print("      # Access at http://localhost:5555")
    
    print("\n🔍 Monitoring Commands:")
    print("   • Check worker status:")
    print("     celery -A agritram inspect active")
    print()
    print("   • Check queue length:")
    print("     redis-cli llen celery")
    print()
    print("   • Purge queue (caution!):")
    print("     celery -A agritram purge")
    print()
    print("   • Health check:")
    print("     python test_celery_email_tasks.py")
    
    print("\n📧 Email Rate Limits:")
    print("   • Activation emails: 100/minute")
    print("   • Welcome emails: 100/minute")
    print("   • Password reset: 50/minute")
    print("   • Security alerts: 50/minute")
    print("   • Bulk notifications: 10/minute")


def show_when_emails_execute():
    """Explain when and how emails are executed"""
    
    print("\n⏰ WHEN EMAILS ARE EXECUTED")
    print("=" * 35)
    
    print("📋 EXECUTION TIMING:")
    print("   • Immediately: When Celery worker is running")
    print("   • Queued: When no worker is available")
    print("   • Retried: On failure (up to 3 times)")
    print("   • Rate Limited: Based on email type limits")
    
    print("\n🔄 EXECUTION PROCESS:")
    print("   1. User registers → Email queued instantly")
    print("   2. Celery worker picks up task (if running)")
    print("   3. Email service sends email via SMTP")
    print("   4. Task marked as complete/failed")
    print("   5. Retry on failure (with backoff)")
    
    print("\n⚡ EXECUTION CONDITIONS:")
    print("   ✅ Redis running: Queue storage available")
    print("   ✅ Celery worker running: Tasks processed")
    print("   ✅ Email config valid: Emails sent successfully")
    print("   ❌ No worker: Tasks queued but not processed")
    print("   ❌ No Redis: Tasks fail to queue")
    print("   ❌ Bad email config: Tasks fail to send")
    
    print("\n📊 RETRY BEHAVIOR:")
    print("   • Max retries: 3 attempts")
    print("   • Retry delay: 60 seconds (first retry)")
    print("   • Backoff: Exponential (60s, 120s, 240s)")
    print("   • Max backoff: 700 seconds (11.7 minutes)")


if __name__ == '__main__':
    print("📧 AGRITRAM EMAIL QUEUE SYSTEM GUIDE")
    print("=" * 50)
    
    explain_email_system()
    check_system_status()
    show_queue_statistics()
    show_when_emails_execute()
    show_management_commands()
    
    print("\n🧪 Want to test the email queue?")
    response = input("Run email queue test? (y/n): ").lower().strip()
    
    if response == 'y':
        test_email_queue()
    
    print("\n📋 SUMMARY:")
    print("✅ Email system uses Celery + Redis for background processing")
    print("✅ Emails are queued immediately and processed by workers")
    print("✅ Rate limiting and retry logic ensure reliable delivery")
    print("✅ Monitoring tools available for queue management")
    
    print("\n🚀 NEXT STEPS:")
    print("1. Ensure Redis is running: redis-server")
    print("2. Start email worker: python manage.py start_celery_worker")
    print("3. Monitor with: celery -A agritram flower")
    print("4. Check logs for email processing status")
