#!/usr/bin/env python3
"""
Test script for fintech-grade password validation
"""

import os
import sys
import django
from django.test import RequestFactory
from django.core.exceptions import ValidationError
import json

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'agritram.settings')
django.setup()

from user.password_validators import (
    validate_fintech_password, 
    FintechPasswordValidator, 
    get_password_strength_score
)
from user.services.registration_validation_service import RegistrationValidationService


def test_password_length_requirements():
    """Test minimum length requirements"""
    print("=== Testing Password Length Requirements ===")
    
    validator = FintechPasswordValidator()
    
    # Test too short passwords
    short_passwords = [
        "Abc123!",      # 7 chars
        "Abc123!@",     # 8 chars  
        "Abc123!@#",    # 9 chars
        "Abc123!@#$"    # 11 chars
    ]
    
    for password in short_passwords:
        try:
            validator.validate(password)
            print(f"✗ Password '{password}' should have failed (too short)")
        except ValidationError as e:
            if "at least 12 characters" in str(e):
                print(f"✓ Correctly rejected short password: {len(password)} chars")
            else:
                print(f"✗ Unexpected error for '{password}': {e}")
    
    # Test valid length
    valid_password = "Abc123!@#$%^"  # 12 chars
    try:
        validator.validate(valid_password)
        print(f"✓ Accepted valid length password: {len(valid_password)} chars")
    except ValidationError as e:
        print(f"✗ Valid length password failed: {e}")


def test_character_composition():
    """Test character composition requirements"""
    print("\n=== Testing Character Composition Requirements ===")
    
    validator = FintechPasswordValidator()
    
    # Test missing character types
    test_cases = [
        ("abcdefghijklmnop", "missing uppercase, digit, special"),
        ("ABCDEFGHIJKLMNOP", "missing lowercase, digit, special"),
        ("123456789012345", "missing uppercase, lowercase, special"),
        ("!@#$%^&*()_+{}[]", "missing uppercase, lowercase, digit"),
        ("Abcdefghijklmnop", "missing digit, special"),
        ("ABC123456789012", "missing lowercase, special"),
        ("abc123!@#$%^&*()", "missing uppercase"),
    ]
    
    for password, expected_missing in test_cases:
        try:
            validator.validate(password)
            print(f"✗ Password '{password[:8]}...' should have failed ({expected_missing})")
        except ValidationError as e:
            if "must contain at least one" in str(e):
                print(f"✓ Correctly rejected password missing: {expected_missing}")
            else:
                print(f"✗ Unexpected error: {e}")
    
    # Test valid composition
    valid_password = "Abc123!@#$%^&*()"
    try:
        validator.validate(valid_password)
        print(f"✓ Accepted password with all character types")
    except ValidationError as e:
        print(f"✗ Valid composition password failed: {e}")


def test_common_password_detection():
    """Test common/breached password detection"""
    print("\n=== Testing Common Password Detection ===")
    
    validator = FintechPasswordValidator()
    
    # Test common passwords
    common_passwords = [
        "Password123!",
        "Welcome123!@",
        "Admin123456!",
        "Qwerty123456!",
        "Dragon123456!",
        "Master123456!",
    ]
    
    for password in common_passwords:
        try:
            validator.validate(password)
            print(f"✗ Common password '{password}' should have been rejected")
        except ValidationError as e:
            if "too common" in str(e) or "data breaches" in str(e):
                print(f"✓ Correctly rejected common password: {password[:8]}...")
            else:
                print(f"✗ Unexpected error for common password: {e}")
    
    # Test secure password
    secure_password = "Tr7$mK9#pL2@vN8!"
    try:
        validator.validate(secure_password)
        print(f"✓ Accepted secure password")
    except ValidationError as e:
        print(f"✗ Secure password failed: {e}")


def test_pattern_detection():
    """Test weak pattern detection"""
    print("\n=== Testing Weak Pattern Detection ===")
    
    validator = FintechPasswordValidator()
    
    # Test weak patterns
    weak_patterns = [
        "Aaaa1234567890!",  # Repeated characters
        "Abc123456789!@#",  # Sequential numbers
        "Qwerty123456!@#",  # Keyboard pattern
        "Password123!@#$",  # Common word
        "MyName1985!@#$%",  # Year pattern
    ]
    
    for password in weak_patterns:
        try:
            validator.validate(password)
            print(f"✗ Weak pattern password '{password[:8]}...' should have been rejected")
        except ValidationError as e:
            if "predictable patterns" in str(e) or "too common" in str(e):
                print(f"✓ Correctly rejected weak pattern: {password[:8]}...")
            else:
                print(f"✗ Unexpected error for weak pattern: {e}")


def test_user_similarity():
    """Test user attribute similarity detection"""
    print("\n=== Testing User Similarity Detection ===")
    
    validator = FintechPasswordValidator()
    
    # Create test user
    class TestUser:
        def __init__(self):
            self.email = "<EMAIL>"
            self.name = "John Doe"
            self.first_name = "John"
            self.last_name = "Doe"
    
    user = TestUser()
    
    # Test similar passwords
    similar_passwords = [
        "John123456!@#$%",      # Contains first name
        "Doe123456!@#$%^",      # Contains last name
        "JohnDoe123456!@",      # Contains full name
        "john123456!@#$%",      # Contains email username
    ]
    
    for password in similar_passwords:
        try:
            validator.validate(password, user)
            print(f"✗ Similar password '{password[:8]}...' should have been rejected")
        except ValidationError as e:
            if "too similar" in str(e):
                print(f"✓ Correctly rejected similar password: {password[:8]}...")
            else:
                print(f"✗ Unexpected error for similar password: {e}")
    
    # Test dissimilar password
    dissimilar_password = "Tr7$mK9#pL2@vN8!"
    try:
        validator.validate(dissimilar_password, user)
        print(f"✓ Accepted dissimilar password")
    except ValidationError as e:
        print(f"✗ Dissimilar password failed: {e}")


def test_password_strength_scoring():
    """Test password strength scoring system"""
    print("\n=== Testing Password Strength Scoring ===")
    
    test_passwords = [
        ("weak", "Weak password with insufficient complexity"),
        ("Abc123!", "Short but has all character types"),
        ("Abc123!@#$%^", "Good length and composition"),
        ("Tr7$mK9#pL2@vN8!", "Very strong password"),
        ("password123", "Common weak password"),
    ]
    
    for password, description in test_passwords:
        score_data = get_password_strength_score(password)
        print(f"Password: {password[:8]}... | Score: {score_data['score']}/100 | "
              f"Strength: {score_data['strength']} | Meets Requirements: {score_data['meets_requirements']}")


def test_registration_integration():
    """Test password validation in registration flow"""
    print("\n=== Testing Registration Integration ===")
    
    factory = RequestFactory()
    
    # Test with weak password
    weak_request = factory.post('/user/register/',
                               data=json.dumps({
                                   'name': 'John Doe',
                                   'email': '<EMAIL>',
                                   'password': 'weak123'  # Too short, missing special chars
                               }),
                               content_type='application/json')
    weak_request.data = {
        'name': 'John Doe',
        'email': '<EMAIL>',
        'password': 'weak123'
    }
    
    try:
        RegistrationValidationService.validate_password_strength(weak_request, '<EMAIL>')
        print("✗ Weak password should have been rejected in registration")
    except Exception as e:
        if "security requirements" in str(e):
            print("✓ Registration correctly rejected weak password")
        else:
            print(f"✗ Unexpected registration error: {e}")
    
    # Test with strong password
    strong_request = factory.post('/user/register/',
                                 data=json.dumps({
                                     'name': 'John Doe',
                                     'email': '<EMAIL>',
                                     'password': 'Tr7$mK9#pL2@vN8!'
                                 }),
                                 content_type='application/json')
    strong_request.data = {
        'name': 'John Doe',
        'email': '<EMAIL>',
        'password': 'Tr7$mK9#pL2@vN8!'
    }
    
    try:
        RegistrationValidationService.validate_password_strength(strong_request, '<EMAIL>')
        print("✓ Registration accepted strong password")
    except Exception as e:
        print(f"✗ Strong password failed registration: {e}")


def main():
    """Run all password validation tests"""
    print("Starting Fintech-Grade Password Validation Tests...\n")
    
    try:
        test_password_length_requirements()
        test_character_composition()
        test_common_password_detection()
        test_pattern_detection()
        test_user_similarity()
        test_password_strength_scoring()
        test_registration_integration()
        
        print("\n🎉 All password validation tests completed!")
        print("\nFintech-Grade Password Requirements:")
        print("✓ Minimum 12 characters")
        print("✓ Must contain uppercase, lowercase, digit, and special character")
        print("✓ Must not be among common/breached passwords")
        print("✓ Must not contain predictable patterns")
        print("✓ Must not be similar to user information")
        print("✓ Must have sufficient entropy/randomness")
        print("✓ Integrated with Django's built-in validation")
        print("✓ Comprehensive logging for security monitoring")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
