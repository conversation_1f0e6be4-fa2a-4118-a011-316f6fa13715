###############################################################################
# Project metadata
###############################################################################
[tool.poetry]
name = "backend"
version = "0.1.0"
description = "Django web back‑end"
authors = ["G <PERSON> Rafi <<EMAIL>>"]
readme = "README.md"
packages = [{ include = "agritram" }]

###############################################################################
# Runtime dependencies  – all at the **exact** versions you requested
###############################################################################
[tool.poetry.dependencies]
python = "^3.12"

# ─── Core web stack ──────────────────────────────────────────────────────────
Django = "5.2"
djangorestframework = "3.16.0"
django-cors-headers = "4.4.0"
asgiref = "3.8.1"
sqlparse = "0.5.1"
tzdata = "2025.2"

# ─── Database ────────────────────────────────────────────────────────────────
psycopg2-binary = "2.9.10"

# ─── Algorand / blockchain ───────────────────────────────────────────────────
algokit-utils = "2.4.0"
py-algorand-sdk = "2.8.0"
pyteal = "0.24.1"
beaker-pyteal = "1.1.1"

# ─── Networking / async ──────────────────────────────────────────────────────
httpx = "0.23.3"
httpcore = "0.16.3"
h11 = "0.14.0"
anyio = "4.8.0"
sniffio = "1.3.1"
idna = "3.10"
certifi = "2025.1.31"
rfc3986 = "1.5.0"

# ─── Cryptography / C‑FFI helpers ────────────────────────────────────────────
cffi = "1.17.1"
pycparser = "2.22"
PyNaCl = "1.5.0"
pycryptodomex = "3.21.0"

# ─── Utilities & helpers ─────────────────────────────────────────────────────
python-dotenv = "1.1.0"
python-decouple = "3.8"
msgpack = "1.1.0"
tabulate = "0.9.0"
dnspython = "2.4.2"
Deprecated = "1.2.18"
docstring-parser = "0.14.1"
executing = "1.2.0"
semantic-version = "2.10.0"
wrapt = "1.17.2"
setuptools = "76.0.0"
typing_extensions = "4.12.2"

# ─── OAuth2 & Security ───────────────────────────────────────────────────────
django-oauth-toolkit = "2.2.0"
PyJWT = "2.8.0"
cryptography = "42.0.5"
django-ratelimit = "4.1.0"
django-extensions = "3.2.3"
pillow = "10.2.0"

# ─── Task Queue ──────────────────────────────────────────────────────────────
celery = "^5.5.3"
redis = "^5.0.1"

# ─── Type‑hint stubs (optional) ──────────────────────────────────────────────
django-stubs = { version = "^5.0", optional = true }
djangorestframework-stubs = { version = "^3.15", optional = true }

###############################################################################
# Development / CI‑only dependencies
###############################################################################
[tool.poetry.group.dev.dependencies]
black = { extras = ["d"], version = "*" }
pytest = "*"
pytest-django = "^4.8"
pytest-cov = "*"
pip-audit = "*"
pre-commit = "*"
puyapy = "*"
colorlog = "^6.9.0"

###############################################################################
# Build backend
###############################################################################
[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
