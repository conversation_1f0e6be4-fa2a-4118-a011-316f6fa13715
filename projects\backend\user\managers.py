from django.contrib.auth.models import BaseUserManager
from django.db import IntegrityError
import logging

logger = logging.getLogger(__name__)


class UserManager(BaseUserManager):
    def create_user(self, email, password=None, **extra_fields):
        from agritram.exceptions import DuplicateResourceException

        if not email:
            raise ValueError("The Email field must be set")

        email = self.normalize_email(email)
        user = self.model(email=email, **extra_fields)
        user.set_password(password)

        try:
            user.save(using=self._db)
            logger.info(
                "User created successfully via manager",
                extra={
                    "operation": "USER_MANAGER_CREATION_SUCCESS",
                    "user_email": email,
                },
            )
            return user

        except IntegrityError as e:
            # Handle race condition for duplicate email
            logger.warning(
                "Race condition detected in UserManager.create_user",
                extra={
                    "operation": "USER_MANAGER_RACE_CONDITION",
                    "email": email,
                    "error": str(e),
                },
            )

            # Check if it's specifically an email uniqueness constraint violation
            if "email" in str(e).lower() and "unique" in str(e).lower():
                raise DuplicateResourceException(
                    message="An account with this email address already exists",
                    details="Another user was created with this email address while your request was being processed.",
                )
            else:
                # Re-raise other integrity errors
                logger.error(
                    "Unexpected integrity error in UserManager.create_user",
                    extra={
                        "operation": "USER_MANAGER_INTEGRITY_ERROR",
                        "email": email,
                        "error": str(e),
                    },
                )
                raise

    def create_superuser(self, email, password=None, **extra_fields):
        extra_fields.setdefault("is_staff", True)
        extra_fields.setdefault("is_superuser", True)

        if extra_fields.get("is_staff") is not True:
            raise ValueError("Superuser must have is_staff=True.")
        if extra_fields.get("is_superuser") is not True:
            raise ValueError("Superuser must have is_superuser=True.")

        return self.create_user(email, password, **extra_fields)
