"""
Async Email Service

This service provides a unified interface for all asynchronous email operations.
All emails are queued using Celery tasks with retry logic and comprehensive logging.
"""

import logging
from typing import Dict, Any, List, Optional
from django.contrib.auth import get_user_model
from celery.result import AsyncResult

logger = logging.getLogger(__name__)
User = get_user_model()


class AsyncEmailService:
    """
    Service for managing asynchronous email delivery using Celery tasks
    """
    
    def send_activation_email_async(
        self, 
        user: User, 
        activation_url: str, 
        user_agent: str, 
        request_metadata: Dict[str, Any] = None
    ) -> AsyncResult:
        """
        Queue activation email for asynchronous delivery
        
        Args:
            user: User instance
            activation_url: Activation URL
            user_agent: User agent string
            request_metadata: Additional metadata from the request
            
        Returns:
            AsyncResult: Celery task result object
        """
        from user.tasks import send_activation_email_task
        
        logger.info(
            "Queueing activation email",
            extra={
                'user_id': user.id,
                'user_email': user.email,
                'service_method': 'send_activation_email_async',
                'event_type': 'ASYNC_EMAIL_SERVICE_QUEUE',
            }
        )
        
        return send_activation_email_task.delay(
            user_id=user.id,
            activation_url=activation_url,
            user_agent=user_agent,
            request_metadata=request_metadata or {}
        )
    
    def send_welcome_email_async(
        self, 
        user: User, 
        ip_address: str, 
        user_agent: str
    ) -> AsyncResult:
        """
        Queue welcome email for asynchronous delivery
        
        Args:
            user: User instance
            ip_address: IP address from activation request
            user_agent: User agent string
            
        Returns:
            AsyncResult: Celery task result object
        """
        from user.tasks import send_welcome_email_task
        
        logger.info(
            "Queueing welcome email",
            extra={
                'user_id': user.id,
                'user_email': user.email,
                'service_method': 'send_welcome_email_async',
                'event_type': 'ASYNC_EMAIL_SERVICE_QUEUE',
            }
        )
        
        return send_welcome_email_task.delay(
            user_id=user.id,
            ip_address=ip_address,
            user_agent=user_agent
        )
    
    def send_password_reset_email_async(
        self, 
        user: User, 
        reset_url: str, 
        ip_address: str = None, 
        user_agent: str = None
    ) -> AsyncResult:
        """
        Queue password reset email for asynchronous delivery
        
        Args:
            user: User instance
            reset_url: Password reset URL
            ip_address: IP address from request
            user_agent: User agent string
            
        Returns:
            AsyncResult: Celery task result object
        """
        from user.tasks import send_password_reset_email_task
        
        logger.info(
            "Queueing password reset email",
            extra={
                'user_id': user.id,
                'user_email': user.email,
                'service_method': 'send_password_reset_email_async',
                'event_type': 'ASYNC_EMAIL_SERVICE_QUEUE',
            }
        )
        
        return send_password_reset_email_task.delay(
            user_id=user.id,
            reset_url=reset_url,
            ip_address=ip_address,
            user_agent=user_agent
        )
    
    def send_security_alert_email_async(
        self, 
        user: User, 
        alert_type: str, 
        details: str, 
        metadata: Dict[str, Any] = None
    ) -> AsyncResult:
        """
        Queue security alert email for asynchronous delivery
        
        Args:
            user: User instance
            alert_type: Type of security alert
            details: Alert details
            metadata: Additional metadata
            
        Returns:
            AsyncResult: Celery task result object
        """
        from user.tasks import send_security_alert_email_task
        
        logger.info(
            "Queueing security alert email",
            extra={
                'user_id': user.id,
                'user_email': user.email,
                'alert_type': alert_type,
                'service_method': 'send_security_alert_email_async',
                'event_type': 'ASYNC_EMAIL_SERVICE_QUEUE',
            }
        )
        
        return send_security_alert_email_task.delay(
            user_id=user.id,
            alert_type=alert_type,
            details=details,
            metadata=metadata or {}
        )
    
    def send_bulk_notification_async(
        self, 
        user_ids: List[int], 
        subject: str, 
        template_name: str, 
        context: Dict[str, Any]
    ) -> AsyncResult:
        """
        Queue bulk notification emails for asynchronous delivery
        
        Args:
            user_ids: List of user IDs to send emails to
            subject: Email subject
            template_name: Email template name
            context: Email context data
            
        Returns:
            AsyncResult: Celery task result object
        """
        from user.tasks import send_bulk_notification_task
        
        logger.info(
            "Queueing bulk notification emails",
            extra={
                'user_count': len(user_ids),
                'subject': subject,
                'template_name': template_name,
                'service_method': 'send_bulk_notification_async',
                'event_type': 'ASYNC_EMAIL_SERVICE_QUEUE',
            }
        )
        
        return send_bulk_notification_task.delay(
            user_ids=user_ids,
            subject=subject,
            template_name=template_name,
            context=context
        )
    
    def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """
        Get the status of an email task
        
        Args:
            task_id: Celery task ID
            
        Returns:
            Dict with task status information
        """
        try:
            result = AsyncResult(task_id)
            
            status_info = {
                'task_id': task_id,
                'status': result.status,
                'ready': result.ready(),
                'successful': result.successful() if result.ready() else None,
                'failed': result.failed() if result.ready() else None,
                'result': result.result if result.ready() else None,
                'traceback': result.traceback if result.failed() else None,
            }
            
            logger.debug(
                "Retrieved task status",
                extra={
                    'task_id': task_id,
                    'status': result.status,
                    'service_method': 'get_task_status',
                    'event_type': 'ASYNC_EMAIL_SERVICE_STATUS_CHECK',
                }
            )
            
            return status_info
            
        except Exception as e:
            logger.error(
                f"Failed to get task status: {e}",
                extra={
                    'task_id': task_id,
                    'error': str(e),
                    'service_method': 'get_task_status',
                    'event_type': 'ASYNC_EMAIL_SERVICE_STATUS_ERROR',
                },
                exc_info=True
            )
            
            return {
                'task_id': task_id,
                'status': 'ERROR',
                'error': str(e)
            }
    
    def get_queue_stats(self) -> Dict[str, Any]:
        """
        Get email queue statistics
        
        Returns:
            Dict with queue statistics
        """
        try:
            from celery import current_app
            
            inspect = current_app.control.inspect()
            stats = inspect.stats()
            active_tasks = inspect.active()
            scheduled_tasks = inspect.scheduled()
            
            queue_stats = {
                'worker_stats': stats,
                'active_tasks_count': len(active_tasks) if active_tasks else 0,
                'scheduled_tasks_count': len(scheduled_tasks) if scheduled_tasks else 0,
                'active_tasks': active_tasks,
                'scheduled_tasks': scheduled_tasks,
            }
            
            logger.info(
                "Retrieved queue statistics",
                extra={
                    'active_tasks_count': queue_stats['active_tasks_count'],
                    'scheduled_tasks_count': queue_stats['scheduled_tasks_count'],
                    'service_method': 'get_queue_stats',
                    'event_type': 'ASYNC_EMAIL_SERVICE_QUEUE_STATS',
                }
            )
            
            return queue_stats
            
        except Exception as e:
            logger.error(
                f"Failed to get queue statistics: {e}",
                extra={
                    'error': str(e),
                    'service_method': 'get_queue_stats',
                    'event_type': 'ASYNC_EMAIL_SERVICE_QUEUE_STATS_ERROR',
                },
                exc_info=True
            )
            
            return {
                'error': str(e),
                'worker_stats': None,
                'active_tasks_count': 0,
                'scheduled_tasks_count': 0,
            }
    
    def retry_failed_task(self, task_id: str) -> Optional[AsyncResult]:
        """
        Retry a failed email task
        
        Args:
            task_id: Celery task ID to retry
            
        Returns:
            AsyncResult: New task result object or None if retry failed
        """
        try:
            result = AsyncResult(task_id)
            
            if result.failed():
                # Get the original task arguments
                # Note: This is a simplified retry - in production you might want
                # to store task arguments in a database for proper retry functionality
                logger.warning(
                    "Manual task retry requested",
                    extra={
                        'original_task_id': task_id,
                        'service_method': 'retry_failed_task',
                        'event_type': 'ASYNC_EMAIL_SERVICE_MANUAL_RETRY',
                    }
                )
                
                # For now, just log the retry request
                # In a full implementation, you'd need to store original task args
                return None
            else:
                logger.info(
                    "Task retry not needed - task not failed",
                    extra={
                        'task_id': task_id,
                        'current_status': result.status,
                        'service_method': 'retry_failed_task',
                        'event_type': 'ASYNC_EMAIL_SERVICE_RETRY_NOT_NEEDED',
                    }
                )
                return result
                
        except Exception as e:
            logger.error(
                f"Failed to retry task: {e}",
                extra={
                    'task_id': task_id,
                    'error': str(e),
                    'service_method': 'retry_failed_task',
                    'event_type': 'ASYNC_EMAIL_SERVICE_RETRY_ERROR',
                },
                exc_info=True
            )
            return None


# Global instance for easy access
async_email_service = AsyncEmailService()
