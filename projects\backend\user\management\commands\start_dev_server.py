#!/usr/bin/env python
"""
Development server startup script

This script starts both Django server and Celery worker with a single command.
Perfect for development - no need to run multiple terminals.
"""

import os
import sys
import subprocess
import threading
import time
import signal
import platform

# Global process references for cleanup
processes = []

def start_django_server():
    """Start Django development server"""
    print("🚀 Starting Django development server...")
    
    try:
        cmd = [sys.executable, 'manage.py', 'runserver', '127.0.0.1:8000']
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            bufsize=1,
            universal_newlines=True
        )
        
        processes.append(('Django Server', process))
        
        # Stream output with prefix
        for line in iter(process.stdout.readline, ''):
            if line.strip():
                print(f"[DJANGO] {line.strip()}")
        
    except Exception as e:
        print(f"❌ Django server failed: {e}")


def start_celery_worker():
    """Start Celery worker"""
    print("📧 Starting Celery email worker...")
    
    # Wait a bit for Django to start first
    time.sleep(3)
    
    try:
        # Windows-compatible command
        is_windows = platform.system() == 'Windows'
        
        if is_windows:
            cmd = [
                'celery', '-A', 'agritram', 'worker',
                '--pool=solo',
                '--queues=email',
                '--loglevel=info',
                '--concurrency=1'
            ]
        else:
            cmd = [
                'celery', '-A', 'agritram', 'worker',
                '--queues=email',
                '--loglevel=info',
                '--concurrency=2'
            ]
        
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            bufsize=1,
            universal_newlines=True
        )
        
        processes.append(('Celery Worker', process))
        
        # Stream output with prefix
        for line in iter(process.stdout.readline, ''):
            if line.strip():
                print(f"[CELERY] {line.strip()}")
        
    except Exception as e:
        print(f"❌ Celery worker failed: {e}")


def check_redis():
    """Check if Redis is running"""
    try:
        import django
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'agritram.settings')
        django.setup()
        
        from django.core.cache import cache
        cache.set('startup_test', 'ok', 10)
        result = cache.get('startup_test')
        return result == 'ok'
    except Exception:
        return False


def cleanup_processes():
    """Clean up all processes"""
    print("\n🛑 Shutting down services...")
    
    for name, process in processes:
        try:
            print(f"   Stopping {name}...")
            process.terminate()
            
            # Wait for graceful shutdown
            try:
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                print(f"   Force killing {name}...")
                process.kill()
                
        except Exception as e:
            print(f"   Error stopping {name}: {e}")
    
    print("✅ All services stopped")


def signal_handler(signum, frame):
    """Handle Ctrl+C gracefully"""
    cleanup_processes()
    sys.exit(0)


def main():
    """Main function to start all services"""
    
    print("🔧 AGRITRAM DEVELOPMENT SERVER STARTER")
    print("=" * 50)
    
    # Check Redis first
    print("🔍 Checking Redis connection...")
    if not check_redis():
        print("❌ Redis is not running!")
        print("\n📋 Please start Redis first:")
        print("   Option 1: Download and install Redis for Windows")
        print("   Option 2: Use Docker: docker run -d -p 6379:6379 redis:alpine")
        print("   Option 3: Use WSL: sudo service redis-server start")
        return
    
    print("✅ Redis is running")
    
    # Set up signal handler for graceful shutdown
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    print("\n🚀 Starting services...")
    print("   Django Server: http://127.0.0.1:8000")
    print("   Celery Worker: Processing email queue")
    print("   Press Ctrl+C to stop all services")
    print()
    
    # Start services in separate threads
    django_thread = threading.Thread(target=start_django_server, daemon=True)
    celery_thread = threading.Thread(target=start_celery_worker, daemon=True)
    
    django_thread.start()
    celery_thread.start()
    
    try:
        # Keep main thread alive
        while True:
            time.sleep(1)
            
            # Check if any process died
            for name, process in processes:
                if process.poll() is not None:
                    print(f"❌ {name} stopped unexpectedly (exit code: {process.returncode})")
                    cleanup_processes()
                    return
                    
    except KeyboardInterrupt:
        signal_handler(None, None)


if __name__ == '__main__':
    # Change to the backend directory
    backend_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(backend_dir)
    
    main()
