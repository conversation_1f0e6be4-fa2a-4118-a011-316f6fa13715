"""
Fintech-Grade Password Validation System

This module provides comprehensive password validation for financial applications,
extending Django's built-in validation with enhanced security requirements.
"""

import re
import logging
from typing import List, Dict, Any
from django.core.exceptions import ValidationError
from django.contrib.auth.password_validation import (
    validate_password as django_validate_password,
)
from django.utils.translation import gettext as _

logger = logging.getLogger(__name__)


class FintechPasswordValidator:
    """
    Fintech-grade password validator with comprehensive security requirements

    Requirements:
    - Minimum 12 characters
    - Must contain uppercase, lowercase, digit, and special character
    - Must not be among common/breached passwords
    - Additional entropy and pattern checks
    """

    # Common breached/weak passwords (subset for demonstration)
    COMMON_PASSWORDS = {
        "password123",
        "admin123456",
        "welcome123",
        "qwerty123456",
        "password1234",
        "administrator",
        "letmein123",
        "monkey123",
        "dragon123456",
        "master123456",
        "trustno1234",
        "football123",
        "baseball123",
        "superman123",
        "michael123456",
        "jennifer123",
        "jordan123456",
        "michelle123",
        "daniel123456",
        "anthony123",
        "joshua123456",
        "matthew123456",
        "andrew123456",
        "christopher123",
        "jessica123456",
        "amanda123456",
        "melissa123456",
        "sarah123456",
        "heather123456",
        "nicole123456",
        "elizabeth123",
        "kimberly123",
        "stephanie123",
        "michelle123456",
        "dorothy123456",
        "lisa123456",
        "nancy123456",
        "karen123456",
        "betty123456",
        "helen123456",
        "sandra123456",
        "donna123456",
        "carol123456",
        "ruth123456",
        "sharon123456",
        "michelle123",
        "laura123456",
        "sarah123456",
        "kimberly123456",
        "deborah123456",
        "jessica123",
        "shirley123456",
        "cynthia123456",
        "angela123456",
        "melissa123",
        "brenda123456",
        "emma123456",
        "olivia123456",
        "ava123456",
        "isabella123456",
        "sophia123456",
        "charlotte123",
        "mia123456",
        "amelia123456",
    }

    # Common password patterns to reject
    WEAK_PATTERNS = [
        r"^(.)\1{3,}",  # Repeated characters (aaaa, 1111, etc.)
        r"^(012|123|234|345|456|567|678|789|890|abc|bcd|cde|def)",  # Sequential
        r"^(qwe|asd|zxc)",  # Keyboard patterns
        r"^(password|admin|user|login|welcome)",  # Common words
        r"(19|20)\d{2}",  # Years
        r"^[a-z]+$",  # Only lowercase
        r"^[A-Z]+$",  # Only uppercase
        r"^\d+$",  # Only digits
    ]

    def __init__(self, min_length=12):
        self.min_length = min_length

    def validate(self, password: str, user=None) -> None:
        """
        Validate password against fintech-grade requirements

        Args:
            password: Password to validate
            user: User instance (optional)

        Raises:
            ValidationError: If password doesn't meet requirements
        """
        errors = []

        # Basic length check
        if len(password) < self.min_length:
            errors.append(
                ValidationError(
                    f"Password must be at least {self.min_length} characters long.",
                    code="password_too_short",
                )
            )

        # Maximum length check (prevent DoS)
        if len(password) > 128:
            errors.append(
                ValidationError(
                    "Password must not exceed 128 characters.",
                    code="password_too_long",
                )
            )

        # Character composition requirements
        composition_errors = self._validate_character_composition(password)
        errors.extend(composition_errors)

        # Common/breached password check
        if self._is_common_password(password):
            errors.append(
                ValidationError(
                    "This password is too common and has been found in data breaches.",
                    code="password_too_common",
                )
            )

        # Weak pattern detection
        pattern_errors = self._validate_patterns(password)
        errors.extend(pattern_errors)

        # User attribute similarity check
        if user:
            similarity_errors = self._validate_user_similarity(password, user)
            errors.extend(similarity_errors)

        # Entropy check
        if self._calculate_entropy(password) < 3.0:
            errors.append(
                ValidationError(
                    "Password lacks sufficient complexity and randomness.",
                    code="password_low_entropy",
                )
            )

        if errors:
            logger.warning(
                "Password validation failed",
                extra={
                    "event_type": "PASSWORD_VALIDATION_FAILURE",
                    "validation_errors": [error.code for error in errors],
                    "password_length": len(password),
                    "user_id": getattr(user, "id", None),
                },
            )
            raise ValidationError(errors)

        logger.info(
            "Password validation successful",
            extra={
                "event_type": "PASSWORD_VALIDATION_SUCCESS",
                "password_length": len(password),
                "entropy_score": self._calculate_entropy(password),
                "user_id": getattr(user, "id", None),
            },
        )

    def _validate_character_composition(self, password: str) -> List[ValidationError]:
        """Validate that password contains required character types"""
        errors = []

        has_upper = bool(re.search(r"[A-Z]", password))
        has_lower = bool(re.search(r"[a-z]", password))
        has_digit = bool(re.search(r"\d", password))
        has_special = bool(
            re.search(r'[!@#$%^&*()_+\-=\[\]{};\':"\\|,.<>\/?`~]', password)
        )

        missing_types = []
        if not has_upper:
            missing_types.append("uppercase letter")
        if not has_lower:
            missing_types.append("lowercase letter")
        if not has_digit:
            missing_types.append("digit")
        if not has_special:
            missing_types.append("special character")

        if missing_types:
            errors.append(
                ValidationError(
                    f"Password must contain at least one: {', '.join(missing_types)}.",
                    code="password_missing_character_types",
                )
            )

        return errors

    def _is_common_password(self, password: str) -> bool:
        """Check if password is in common/breached password list"""
        # Check exact matches (case-insensitive)
        if password.lower() in self.COMMON_PASSWORDS:
            return True

        # Check variations with common substitutions
        variations = self._generate_common_variations(password.lower())
        return any(var in self.COMMON_PASSWORDS for var in variations)

    def _generate_common_variations(self, password: str) -> List[str]:
        """Generate common password variations for checking"""
        variations = [password]

        # Common substitutions
        substitutions = {
            "@": "a",
            "3": "e",
            "1": "i",
            "0": "o",
            "5": "s",
            "7": "t",
            "4": "a",
            "8": "b",
            "6": "g",
            "2": "z",
        }

        for char, replacement in substitutions.items():
            if char in password:
                variations.append(password.replace(char, replacement))

        return variations

    def _validate_patterns(self, password: str) -> List[ValidationError]:
        """Validate against weak password patterns"""
        errors = []

        for pattern in self.WEAK_PATTERNS:
            if re.search(pattern, password.lower()):
                errors.append(
                    ValidationError(
                        "Password contains predictable patterns or sequences.",
                        code="password_weak_pattern",
                    )
                )
                break  # Only report one pattern error

        return errors

    def _validate_user_similarity(self, password: str, user) -> List[ValidationError]:
        """Check if password is too similar to user attributes"""
        errors = []

        if not user:
            return errors

        # Check against user attributes
        user_attrs = [
            getattr(user, "email", "").split("@")[0],  # Email username part
            getattr(user, "name", ""),
            getattr(user, "first_name", ""),
            getattr(user, "last_name", ""),
        ]

        for attr in user_attrs:
            if attr and len(attr) > 3:
                if attr.lower() in password.lower():
                    errors.append(
                        ValidationError(
                            "Password is too similar to your personal information.",
                            code="password_too_similar",
                        )
                    )
                    break

        return errors

    def _calculate_entropy(self, password: str) -> float:
        """Calculate password entropy (bits per character)"""
        if not password:
            return 0.0

        # Estimate character space
        char_space = 0
        if re.search(r"[a-z]", password):
            char_space += 26
        if re.search(r"[A-Z]", password):
            char_space += 26
        if re.search(r"\d", password):
            char_space += 10
        if re.search(r'[!@#$%^&*()_+\-=\[\]{};\':"\\|,.<>\/?`~]', password):
            char_space += 32

        # Calculate entropy
        import math

        if char_space > 0:
            entropy = math.log2(char_space) * len(password) / len(password)
            return entropy

        return 0.0

    def get_help_text(self) -> str:
        """Return help text for password requirements"""
        return _(
            f"Your password must be at least {self.min_length} characters long and contain "
            "at least one uppercase letter, one lowercase letter, one digit, and one special character. "
            "It must not be a common or previously breached password."
        )


def validate_fintech_password(password: str, user=None) -> None:
    """
    Comprehensive password validation for fintech applications

    This function combines Django's built-in validation with enhanced
    fintech-grade security requirements.

    Args:
        password: Password to validate
        user: User instance (optional)

    Raises:
        ValidationError: If password doesn't meet requirements
    """
    # First run Django's built-in validation
    try:
        django_validate_password(password, user)
    except ValidationError as e:
        # Re-raise Django validation errors
        logger.warning(
            "Django password validation failed",
            extra={
                "event_type": "DJANGO_PASSWORD_VALIDATION_FAILURE",
                "errors": e.messages,
                "user_id": getattr(user, "id", None),
            },
        )
        raise

    # Then run our enhanced fintech validation
    validator = FintechPasswordValidator()
    validator.validate(password, user)

    logger.info(
        "Complete password validation successful",
        extra={
            "event_type": "COMPLETE_PASSWORD_VALIDATION_SUCCESS",
            "password_length": len(password),
            "user_id": getattr(user, "id", None),
        },
    )


def get_password_strength_score(password: str) -> Dict[str, Any]:
    """
    Calculate password strength score and provide feedback

    Args:
        password: Password to analyze

    Returns:
        Dict containing strength score and feedback
    """
    score = 0
    feedback = []

    # Length scoring
    if len(password) >= 12:
        score += 25
    elif len(password) >= 8:
        score += 15
        feedback.append("Consider using a longer password (12+ characters)")
    else:
        feedback.append("Password is too short (minimum 12 characters)")

    # Character composition scoring
    has_upper = bool(re.search(r"[A-Z]", password))
    has_lower = bool(re.search(r"[a-z]", password))
    has_digit = bool(re.search(r"\d", password))
    has_special = bool(re.search(r'[!@#$%^&*()_+\-=\[\]{};\':"\\|,.<>\/?`~]', password))

    char_types = sum([has_upper, has_lower, has_digit, has_special])
    score += char_types * 15

    if char_types < 4:
        missing = []
        if not has_upper:
            missing.append("uppercase letters")
        if not has_lower:
            missing.append("lowercase letters")
        if not has_digit:
            missing.append("digits")
        if not has_special:
            missing.append("special characters")
        feedback.append(f"Add {', '.join(missing)}")

    # Entropy scoring
    validator = FintechPasswordValidator()
    entropy = validator._calculate_entropy(password)
    if entropy >= 4.0:
        score += 20
    elif entropy >= 3.0:
        score += 10
        feedback.append("Increase password complexity")
    else:
        feedback.append("Password lacks sufficient randomness")

    # Common password penalty
    if validator._is_common_password(password):
        score -= 30
        feedback.append("This password is too common")

    # Pattern penalty
    pattern_errors = validator._validate_patterns(password)
    if pattern_errors:
        score -= 20
        feedback.append("Avoid predictable patterns")

    # Normalize score
    score = max(0, min(100, score))

    # Determine strength level
    if score >= 80:
        strength = "Very Strong"
    elif score >= 60:
        strength = "Strong"
    elif score >= 40:
        strength = "Moderate"
    elif score >= 20:
        strength = "Weak"
    else:
        strength = "Very Weak"

    return {
        "score": score,
        "strength": strength,
        "feedback": feedback,
        "meets_requirements": score >= 60 and len(feedback) == 0,
    }
