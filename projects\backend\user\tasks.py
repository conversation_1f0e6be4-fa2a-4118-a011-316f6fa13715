"""
Celery Tasks for User Email Operations

This module contains all asynchronous email tasks including:
- Activation emails
- Welcome emails
- Password reset emails
- Security alerts
- Bulk notifications

All tasks include retry logic with exponential backoff and comprehensive logging.
"""

import logging
from typing import Dict, Any, List
from celery import shared_task
from django.utils import timezone
from django.contrib.auth import get_user_model
from oauth2_auth.email_service import email_service

logger = logging.getLogger(__name__)
User = get_user_model()


@shared_task(
    bind=True,
    autoretry_for=(Exception,),
    retry_kwargs={"max_retries": 3, "countdown": 60},
    retry_backoff=True,
    retry_backoff_max=700,
    retry_jitter=False,
    name="user.tasks.send_activation_email_task",
)
def send_activation_email_task(
    self,
    user_id: int,
    activation_url: str,
    user_agent: str,
    request_metadata: Dict[str, Any] = None,
):
    """
    Send activation email asynchronously

    Args:
        user_id: User ID to send email to
        activation_url: Activation URL to include in email
        user_agent: User agent string from request
        request_metadata: Additional metadata from the request

    Returns:
        Dict with success status and details
    """
    task_start_time = timezone.now()

    try:
        # Get user instance
        try:
            user = User.objects.get(id=user_id)
        except User.DoesNotExist:
            error_msg = f"User with ID {user_id} not found"
            logger.error(
                "Activation email task failed - user not found",
                extra={
                    "task_id": self.request.id,
                    "user_id": user_id,
                    "error": error_msg,
                    "event_type": "ACTIVATION_EMAIL_TASK_USER_NOT_FOUND",
                    "retry_count": self.request.retries,
                },
            )
            return {
                "success": False,
                "error": error_msg,
                "task_id": self.request.id,
                "user_id": user_id,
            }

        # Log task start
        logger.info(
            "Starting activation email task",
            extra={
                "task_id": self.request.id,
                "user_id": user.id,
                "user_email": user.email,
                "activation_url": activation_url,
                "user_agent": user_agent,
                "event_type": "ACTIVATION_EMAIL_TASK_START",
                "retry_count": self.request.retries,
                "request_metadata": request_metadata or {},
            },
        )

        # Send the email
        email_success = email_service.send_registration_activation(
            user=user, activation_url=activation_url, user_agent=user_agent
        )

        task_duration = (timezone.now() - task_start_time).total_seconds()

        if email_success:
            logger.info(
                "Activation email sent successfully",
                extra={
                    "task_id": self.request.id,
                    "user_id": user.id,
                    "user_email": user.email,
                    "task_duration_seconds": task_duration,
                    "event_type": "ACTIVATION_EMAIL_TASK_SUCCESS",
                    "retry_count": self.request.retries,
                },
            )

            return {
                "success": True,
                "task_id": self.request.id,
                "user_id": user.id,
                "user_email": user.email,
                "task_duration_seconds": task_duration,
            }
        else:
            error_msg = "Email service returned failure"
            logger.warning(
                "Activation email task completed but email service failed",
                extra={
                    "task_id": self.request.id,
                    "user_id": user.id,
                    "user_email": user.email,
                    "error": error_msg,
                    "task_duration_seconds": task_duration,
                    "event_type": "ACTIVATION_EMAIL_TASK_EMAIL_FAILURE",
                    "retry_count": self.request.retries,
                },
            )

            # Retry the task
            raise Exception(error_msg)

    except Exception as exc:
        task_duration = (timezone.now() - task_start_time).total_seconds()

        logger.error(
            f"Activation email task failed: {exc}",
            extra={
                "task_id": self.request.id,
                "user_id": user_id,
                "error": str(exc),
                "task_duration_seconds": task_duration,
                "event_type": "ACTIVATION_EMAIL_TASK_FAILURE",
                "retry_count": self.request.retries,
                "max_retries": self.max_retries,
            },
            exc_info=True,
        )

        # If we've exhausted retries, log final failure
        if self.request.retries >= self.max_retries:
            logger.error(
                "Activation email task failed permanently after all retries",
                extra={
                    "task_id": self.request.id,
                    "user_id": user_id,
                    "error": str(exc),
                    "final_retry_count": self.request.retries,
                    "event_type": "ACTIVATION_EMAIL_TASK_PERMANENT_FAILURE",
                },
            )

            return {
                "success": False,
                "error": str(exc),
                "task_id": self.request.id,
                "user_id": user_id,
                "permanent_failure": True,
            }

        # Re-raise to trigger retry
        raise


@shared_task(
    bind=True,
    autoretry_for=(Exception,),
    retry_kwargs={"max_retries": 3, "countdown": 60},
    retry_backoff=True,
    retry_backoff_max=700,
    name="user.tasks.send_welcome_email_task",
)
def send_welcome_email_task(self, user_id: int, ip_address: str, user_agent: str):
    """
    Send welcome email asynchronously after account activation

    Args:
        user_id: User ID to send email to
        ip_address: IP address from activation request
        user_agent: User agent string from request

    Returns:
        Dict with success status and details
    """
    task_start_time = timezone.now()

    try:
        # Get user instance
        try:
            user = User.objects.get(id=user_id)
        except User.DoesNotExist:
            error_msg = f"User with ID {user_id} not found"
            logger.error(
                "Welcome email task failed - user not found",
                extra={
                    "task_id": self.request.id,
                    "user_id": user_id,
                    "error": error_msg,
                    "event_type": "WELCOME_EMAIL_TASK_USER_NOT_FOUND",
                },
            )
            return {"success": False, "error": error_msg}

        logger.info(
            "Starting welcome email task",
            extra={
                "task_id": self.request.id,
                "user_id": user.id,
                "user_email": user.email,
                "event_type": "WELCOME_EMAIL_TASK_START",
            },
        )

        # Send the email
        email_success = email_service.send_welcome_email(
            user=user, ip_address=ip_address, user_agent=user_agent
        )

        task_duration = (timezone.now() - task_start_time).total_seconds()

        if email_success:
            logger.info(
                "Welcome email sent successfully",
                extra={
                    "task_id": self.request.id,
                    "user_id": user.id,
                    "user_email": user.email,
                    "task_duration_seconds": task_duration,
                    "event_type": "WELCOME_EMAIL_TASK_SUCCESS",
                },
            )

            return {
                "success": True,
                "task_id": self.request.id,
                "user_id": user.id,
                "task_duration_seconds": task_duration,
            }
        else:
            error_msg = "Email service returned failure"
            logger.warning(
                "Welcome email task failed",
                extra={
                    "task_id": self.request.id,
                    "user_id": user.id,
                    "error": error_msg,
                    "event_type": "WELCOME_EMAIL_TASK_EMAIL_FAILURE",
                },
            )
            raise Exception(error_msg)

    except Exception as exc:
        task_duration = (timezone.now() - task_start_time).total_seconds()

        logger.error(
            f"Welcome email task failed: {exc}",
            extra={
                "task_id": self.request.id,
                "user_id": user_id,
                "error": str(exc),
                "task_duration_seconds": task_duration,
                "event_type": "WELCOME_EMAIL_TASK_FAILURE",
                "retry_count": self.request.retries,
            },
            exc_info=True,
        )

        # Re-raise to trigger retry
        raise


@shared_task(
    bind=True,
    autoretry_for=(Exception,),
    retry_kwargs={"max_retries": 3, "countdown": 60},
    retry_backoff=True,
    retry_backoff_max=700,
    name="user.tasks.send_password_reset_email_task",
)
def send_password_reset_email_task(
    self, user_id: int, reset_url: str, ip_address: str = None, user_agent: str = None
):
    """
    Send password reset email asynchronously

    Args:
        user_id: User ID to send email to
        reset_url: Password reset URL
        ip_address: IP address from request
        user_agent: User agent string from request

    Returns:
        Dict with success status and details
    """
    try:
        user = User.objects.get(id=user_id)

        logger.info(
            "Starting password reset email task",
            extra={
                "task_id": self.request.id,
                "user_id": user.id,
                "user_email": user.email,
                "event_type": "PASSWORD_RESET_EMAIL_TASK_START",
            },
        )

        email_success = email_service.send_password_reset(
            user=user, reset_url=reset_url, ip_address=ip_address, user_agent=user_agent
        )

        if email_success:
            logger.info(
                "Password reset email sent successfully",
                extra={
                    "task_id": self.request.id,
                    "user_id": user.id,
                    "event_type": "PASSWORD_RESET_EMAIL_TASK_SUCCESS",
                },
            )
            return {"success": True, "task_id": self.request.id}
        else:
            raise Exception("Email service returned failure")

    except User.DoesNotExist:
        logger.error(
            "Password reset email task failed - user not found",
            extra={
                "task_id": self.request.id,
                "user_id": user_id,
                "event_type": "PASSWORD_RESET_EMAIL_TASK_USER_NOT_FOUND",
            },
        )
        return {"success": False, "error": "User not found"}

    except Exception as exc:
        logger.error(
            f"Password reset email task failed: {exc}",
            extra={
                "task_id": self.request.id,
                "user_id": user_id,
                "error": str(exc),
                "event_type": "PASSWORD_RESET_EMAIL_TASK_FAILURE",
            },
            exc_info=True,
        )
        raise


@shared_task(
    bind=True,
    autoretry_for=(Exception,),
    retry_kwargs={"max_retries": 3, "countdown": 60},
    retry_backoff=True,
    retry_backoff_max=700,
    name="user.tasks.send_security_alert_email_task",
)
def send_security_alert_email_task(
    self, user_id: int, alert_type: str, details: str, metadata: Dict[str, Any] = None
):
    """
    Send security alert email asynchronously

    Args:
        user_id: User ID to send email to
        alert_type: Type of security alert
        details: Alert details
        metadata: Additional metadata

    Returns:
        Dict with success status and details
    """
    try:
        user = User.objects.get(id=user_id)

        logger.info(
            "Starting security alert email task",
            extra={
                "task_id": self.request.id,
                "user_id": user.id,
                "alert_type": alert_type,
                "event_type": "SECURITY_ALERT_EMAIL_TASK_START",
            },
        )

        email_success = email_service.send_security_alert(
            user=user, alert_type=alert_type, details=details, metadata=metadata
        )

        if email_success:
            logger.info(
                "Security alert email sent successfully",
                extra={
                    "task_id": self.request.id,
                    "user_id": user.id,
                    "alert_type": alert_type,
                    "event_type": "SECURITY_ALERT_EMAIL_TASK_SUCCESS",
                },
            )
            return {"success": True, "task_id": self.request.id}
        else:
            raise Exception("Email service returned failure")

    except User.DoesNotExist:
        logger.error(
            "Security alert email task failed - user not found",
            extra={
                "task_id": self.request.id,
                "user_id": user_id,
                "event_type": "SECURITY_ALERT_EMAIL_TASK_USER_NOT_FOUND",
            },
        )
        return {"success": False, "error": "User not found"}

    except Exception as exc:
        logger.error(
            f"Security alert email task failed: {exc}",
            extra={
                "task_id": self.request.id,
                "user_id": user_id,
                "error": str(exc),
                "event_type": "SECURITY_ALERT_EMAIL_TASK_FAILURE",
            },
            exc_info=True,
        )
        raise


@shared_task(
    bind=True,
    autoretry_for=(Exception,),
    retry_kwargs={"max_retries": 2, "countdown": 120},
    retry_backoff=True,
    retry_backoff_max=1800,
    name="user.tasks.send_bulk_notification_task",
)
def send_bulk_notification_task(
    self, user_ids: List[int], subject: str, template_name: str, context: Dict[str, Any]
):
    """
    Send bulk notification emails asynchronously

    Args:
        user_ids: List of user IDs to send emails to
        subject: Email subject
        template_name: Email template name
        context: Email context data

    Returns:
        Dict with success/failure counts and details
    """
    task_start_time = timezone.now()

    try:
        logger.info(
            "Starting bulk notification email task",
            extra={
                "task_id": self.request.id,
                "user_count": len(user_ids),
                "subject": subject,
                "template_name": template_name,
                "event_type": "BULK_NOTIFICATION_EMAIL_TASK_START",
            },
        )

        # Get users
        users = User.objects.filter(id__in=user_ids)
        found_user_ids = set(users.values_list("id", flat=True))
        missing_user_ids = set(user_ids) - found_user_ids

        if missing_user_ids:
            logger.warning(
                f"Some users not found for bulk notification: {missing_user_ids}",
                extra={
                    "task_id": self.request.id,
                    "missing_user_ids": list(missing_user_ids),
                    "event_type": "BULK_NOTIFICATION_EMAIL_TASK_MISSING_USERS",
                },
            )

        # Send bulk emails
        results = email_service.send_bulk_notification(
            users=list(users),
            subject=subject,
            template_name=template_name,
            context=context,
        )

        task_duration = (timezone.now() - task_start_time).total_seconds()

        logger.info(
            "Bulk notification email task completed",
            extra={
                "task_id": self.request.id,
                "total_users": len(users),
                "success_count": results.get("success", 0),
                "failed_count": results.get("failed", 0),
                "task_duration_seconds": task_duration,
                "event_type": "BULK_NOTIFICATION_EMAIL_TASK_SUCCESS",
            },
        )

        return {
            "success": True,
            "task_id": self.request.id,
            "results": results,
            "task_duration_seconds": task_duration,
            "missing_user_ids": list(missing_user_ids),
        }

    except Exception as exc:
        task_duration = (timezone.now() - task_start_time).total_seconds()

        logger.error(
            f"Bulk notification email task failed: {exc}",
            extra={
                "task_id": self.request.id,
                "user_count": len(user_ids),
                "error": str(exc),
                "task_duration_seconds": task_duration,
                "event_type": "BULK_NOTIFICATION_EMAIL_TASK_FAILURE",
            },
            exc_info=True,
        )
        raise


@shared_task(bind=True, name="user.tasks.cleanup_failed_email_tasks")
def cleanup_failed_email_tasks(self):
    """
    Periodic task to clean up failed email task records and log statistics

    Returns:
        Dict with cleanup statistics
    """
    try:
        from celery import current_app

        # Get task statistics
        inspect = current_app.control.inspect()
        stats = inspect.stats()

        # Log cleanup activity
        logger.info(
            "Email task cleanup started",
            extra={
                "task_id": self.request.id,
                "worker_stats": stats,
                "event_type": "EMAIL_TASK_CLEANUP_START",
            },
        )

        # Here you could add logic to clean up old task results
        # For now, we'll just log the statistics

        cleanup_time = timezone.now()

        logger.info(
            "Email task cleanup completed",
            extra={
                "task_id": self.request.id,
                "cleanup_time": cleanup_time.isoformat(),
                "event_type": "EMAIL_TASK_CLEANUP_SUCCESS",
            },
        )

        return {
            "success": True,
            "cleanup_time": cleanup_time.isoformat(),
            "stats": stats,
        }

    except Exception as exc:
        logger.error(
            f"Email task cleanup failed: {exc}",
            extra={
                "task_id": self.request.id,
                "error": str(exc),
                "event_type": "EMAIL_TASK_CLEANUP_FAILURE",
            },
            exc_info=True,
        )
        return {"success": False, "error": str(exc)}
