version: '3.8'

services:
  # Redis for Celery message broker
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3

  # Django development server
  django:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
      - ./frontend/dist:/app/static/frontend
    environment:
      - DEBUG=True
      - DJANGO_SETTINGS_MODULE=agritram.settings
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    depends_on:
      redis:
        condition: service_healthy
    command: python manage.py runserver 0.0.0.0:8000
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health/"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Celery worker for email processing
  celery-worker:
    build:
      context: ./backend
      dockerfile: Dockerfile
    volumes:
      - ./backend:/app
    environment:
      - DEBUG=True
      - DJANGO_SETTINGS_MODULE=agritram.settings
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    depends_on:
      redis:
        condition: service_healthy
      django:
        condition: service_healthy
    command: celery -A agritram worker --queues=email --loglevel=info --concurrency=2
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "celery", "-A", "agritram", "inspect", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Celery Flower for monitoring (optional)
  flower:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "5555:5555"
    volumes:
      - ./backend:/app
    environment:
      - DEBUG=True
      - DJANGO_SETTINGS_MODULE=agritram.settings
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    depends_on:
      redis:
        condition: service_healthy
    command: celery -A agritram flower --port=5555
    restart: unless-stopped
    profiles:
      - monitoring

volumes:
  redis_data:
